#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🛠️ 工具函数模块
集中管理通用工具函数，遵循KISS原则
"""

import asyncio
import json
import re
import logging
import os
import threading
from typing import Dict, Any, List, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

# ==================== JSON处理工具 ====================

def fix_common_json_errors(json_str: str) -> str:
    """修复常见的JSON格式错误"""
    # 移除多余的文本
    json_str = re.sub(r'^[^{]*', '', json_str)  # 移除开头非JSON部分
    json_str = re.sub(r'[^}]*$', '', json_str)  # 移除结尾非JSON部分
    
    # 修复单引号
    json_str = json_str.replace("'", '"')
    
    # 修复缺少引号的键
    json_str = re.sub(r'(\w+):', r'"\1":', json_str)
    
    return json_str


def clean_json_response(response: str) -> str:
    """清理JSON响应，移除markdown等格式"""
    # 移除markdown代码块
    response = re.sub(r'```json\s*', '', response)
    response = re.sub(r'```', '', response)
    
    # 提取JSON部分
    json_match = re.search(r'\{.*\}', response, re.DOTALL)
    if json_match:
        json_str = json_match.group()
        # 修复常见错误
        json_str = json_str.replace("'", '"')  # 单引号转双引号
        json_str = re.sub(r',\s*}', '}', json_str)  # 移除尾随逗号
        json_str = re.sub(r',\s*]', ']', json_str)  # 移除数组尾随逗号
        return json_str
    return "{}"


def parse_json_with_fallback(json_str: str, default_value: Any = None) -> Any:
    """带容错的JSON解析"""
    try:
        return json.loads(json_str)
    except json.JSONDecodeError:
        try:
            # 尝试修复常见错误
            fixed_json = fix_common_json_errors(json_str)
            return json.loads(fixed_json)
        except Exception:
            logger.warning("JSON解析失败，返回默认值")
            return default_value


# ==================== 异步文件操作工具 ====================

# 检测aiofiles可用性
try:
    import aiofiles
    AIOFILES_AVAILABLE = True
except ImportError:
    AIOFILES_AVAILABLE = False


async def async_read_json(file_path: str) -> Any:
    """异步读取JSON文件"""
    if AIOFILES_AVAILABLE:
        async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
            content = await f.read()
            return json.loads(content)
    else:
        # 回退到线程池中的同步操作
        def _sync_read():
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)

        import concurrent.futures
        with concurrent.futures.ThreadPoolExecutor() as executor:
            return await asyncio.get_event_loop().run_in_executor(executor, _sync_read)


async def async_write_json(file_path: str, data: Any) -> None:
    """异步写入JSON文件"""
    if AIOFILES_AVAILABLE:
        async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
            content = json.dumps(data, ensure_ascii=False, indent=2)
            await f.write(content)
    else:
        # 回退到线程池中的同步操作
        def _sync_write():
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

        import concurrent.futures
        with concurrent.futures.ThreadPoolExecutor() as executor:
            await asyncio.get_event_loop().run_in_executor(executor, _sync_write)


# ==================== 异步任务管理工具 ====================

async def safe_cancel_tasks(tasks: List[asyncio.Task]) -> None:
    """安全取消任务列表"""
    cancelled_tasks = []
    for task in tasks:
        if not task.done():
            task.cancel()
            cancelled_tasks.append(task)

    # 等待被取消的任务完成清理
    if cancelled_tasks:
        await asyncio.gather(*cancelled_tasks, return_exceptions=True)


async def safe_cleanup_tasks(tasks: List[asyncio.Task]) -> None:
    """确保任务完全清理"""
    if not tasks:
        return

    # 取消所有未完成的任务
    for task in tasks:
        if not task.done():
            task.cancel()

    # 等待所有任务完成（包括清理）
    await asyncio.gather(*tasks, return_exceptions=True)


def with_timeout(timeout_seconds: int):
    """为异步函数添加超时保护的装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            try:
                return await asyncio.wait_for(func(*args, **kwargs), timeout=timeout_seconds)
            except asyncio.TimeoutError:
                logger.warning(f"🚨 函数 {func.__name__} 执行超时({timeout_seconds}s)")
                raise
        return wrapper
    return decorator


# ==================== 进度管理工具 ====================

class ProgressManager:
    """🎯 进度管理器 - 美化和管理进度显示的逻辑"""

    def __init__(self):
        self._lock = threading.Lock()
        self._current_stage = None
        self._stage_name = ""
        self._total_tasks = 0
        self._completed = 0
        self._success = 0
        self._failed = 0
        self._start_time = None
        self._spinner_frame = 0
        self._animation_frame = 0

        # 旋转器字符
        self._spinner_chars = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏']

        # ANSI转义序列
        self._CLEAR_LINE = '\033[2K'
        self._CURSOR_UP = '\033[1A'
        self._CURSOR_DOWN = '\033[1B'
        self._SAVE_CURSOR = '\033[s'
        self._RESTORE_CURSOR = '\033[u'

    def start_stage(self, stage_name: str, total_tasks: int = 0):
        """开始一个新阶段"""
        with self._lock:
            self._stage_name = stage_name
            self._total_tasks = total_tasks
            self._completed = 0
            self._success = 0
            self._failed = 0
            self._start_time = datetime.now()
            self._current_stage = True

            # 显示阶段开始信息
            print(f"\n{stage_name}")
            if total_tasks > 0:
                self._render_progress()

    def update_progress(self, completed: Optional[int] = None, success: Optional[int] = None,
                       failed: Optional[int] = None, increment: bool = False):
        """更新进度"""
        with self._lock:
            if not self._current_stage:
                return

            if increment:
                # 增量模式
                if completed is not None:
                    self._completed += completed
                if success is not None:
                    self._success += success
                if failed is not None:
                    self._failed += failed
            else:
                # 绝对值模式
                if completed is not None:
                    self._completed = completed
                if success is not None:
                    self._success = success
                if failed is not None:
                    self._failed = failed

            self._render_progress()

    def finish_stage(self, final_message: Optional[str] = None):
        """结束当前阶段"""
        with self._lock:
            if not self._current_stage:
                return

            self._current_stage = False

            # 显示最终进度
            self._render_progress(final=True)

            # 显示完成信息
            if final_message:
                print(f"✅ {final_message}")

            # 计算耗时
            if self._start_time:
                duration = datetime.now() - self._start_time
                print(f"⏱️  耗时: {duration.total_seconds():.1f}秒")

            print()  # 额外换行

    def _render_progress(self, final: bool = False, width: int = 50):
        """渲染进度条"""
        if self._total_tasks <= 0:
            # 无总数的简单进度显示
            if final:
                spinner = "✅"
            else:
                self._spinner_frame = (self._spinner_frame + 1) % len(self._spinner_chars)
                spinner = self._spinner_chars[self._spinner_frame]

            status_parts = []
            if self._completed > 0:
                status_parts.append(f"已完成: {self._completed}")
            if self._success > 0:
                status_parts.append(f"成功: {self._success}")
            if self._failed > 0:
                status_parts.append(f"失败: {self._failed}")

            status = " | ".join(status_parts) if status_parts else "处理中..."
            print(f"\r{spinner} {status}", end='', flush=True)

            if final:
                print()  # 完成时换行
            return

        # 有总数的详细进度条
        percentage = self._completed / self._total_tasks if self._total_tasks > 0 else 0
        filled = int(width * percentage)

        # 动态效果：让已填充部分有流动效果
        if not final and self._completed < self._total_tasks and filled > 0:
            self._animation_frame = (self._animation_frame + 1) % 3
            if self._animation_frame == 0:
                bar = '█' * filled + '░' * (width - filled)
            elif self._animation_frame == 1:
                bar = '▓' * filled + '░' * (width - filled)
            else:
                bar = '▒' * filled + '░' * (width - filled)
        else:
            # 完成时显示实心
            bar = '█' * filled + '░' * (width - filled)

        # 旋转器
        if final:
            spinner = "✅"
        else:
            self._spinner_frame = (self._spinner_frame + 1) % len(self._spinner_chars)
            spinner = self._spinner_chars[self._spinner_frame]

        # 构建状态信息
        status_parts = [f"{self._completed}/{self._total_tasks}"]
        if self._success > 0:
            status_parts.append(f"成功: {self._success}")
        if self._failed > 0:
            status_parts.append(f"失败: {self._failed}")

        status = " | ".join(status_parts)

        # 显示进度条
        print(f"\r{spinner} [{bar}] {percentage:.1%} ({status})", end='', flush=True)

        if final:
            print()  # 完成时换行

    def log_message(self, message: str):
        """在不干扰进度条的情况下输出日志信息"""
        with self._lock:
            if self._current_stage:
                # 清除当前进度条，输出消息，然后在新行重新渲染进度条
                print(f"\r{self._CLEAR_LINE}", end="")  # 清除当前行
                print(message)  # 输出消息并自动换行
                self._render_progress()
            else:
                print(message)


# ==================== 日志设置工具 ====================

def setup_logging(level: str = "WARNING"):
    """设置日志配置"""
    import sys
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S',
        stream=sys.stderr
    )


# ==================== 字符串处理工具 ====================

def extract_entities_by_pattern(response: str, entity_types: List[str]) -> Dict[str, List[str]]:
    """基于模式提取实体（备用方案）"""
    entities = {etype: [] for etype in entity_types}
    
    # 简单模式匹配提取
    for etype in entity_types:
        pattern = rf'{etype}["\s]*:[\s]*\[(.*?)\]'
        matches = re.findall(pattern, response, re.IGNORECASE | re.DOTALL)
        if matches:
            for match in matches:
                items = re.findall(r'"([^"]+)"', match)
                entities[etype].extend(items)
    
    return entities


def print_banner():
    """打印系统横幅"""
    print("=" * 60)
    print("🧠 APIICL - 元认知智能体NER系统 (三阶段处理)")
    print("📚 阶段1: 生成检索请求 → 阶段2: 执行检索 → 阶段3: 执行NER")
    print("=" * 60)